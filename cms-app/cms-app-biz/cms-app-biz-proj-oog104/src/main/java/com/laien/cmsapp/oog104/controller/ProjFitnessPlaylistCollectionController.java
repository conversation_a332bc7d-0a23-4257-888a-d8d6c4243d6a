package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.request.ProjFitnessPlaylistCollectionListReq;
import com.laien.cmsapp.oog104.service.ProjFitnessPlaylistCollectionPubService;
import com.laien.cmsapp.oog104.vo.ProjFitnessPlaylistCollectionVO;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Music Playlist Collection API
 *
 * <AUTHOR>
 * @since 2025/09/01
 */
@Api(value = "/{appCode}/fitnessPlaylistCollection", tags = {"app端：Music Playlist Collection"})
@RestController
@RequestMapping("/{appCode}/fitnessPlaylistCollection")
public class ProjFitnessPlaylistCollectionController extends ResponseController {

    @Resource
    private ProjFitnessPlaylistCollectionPubService service;

    @ApiOperation(value = "Music Playlist Collection查询接口", tags = {"oog104"})
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjFitnessPlaylistCollectionVO>> list(ProjFitnessPlaylistCollectionListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjFitnessPlaylistCollectionVO> result = service.list(versionInfoBO, req);
        return succ(result);
    }
}
